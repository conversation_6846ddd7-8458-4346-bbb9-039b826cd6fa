import { jsPDF } from 'jspdf'
import { ISelectOption } from '~/core/ui/Select'
import { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import {
  IResume,
  ISectionCustomFieldParamType
} from '~/lib/features/settings/profiles/edit/types'
import {
  FONT_SIZE_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'
import { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import mappingData from '../utilities/mappingData'
import tableRenderer, {
  estimateRichContentHeight,
  renderRichContentToPDF,
  drawBorderCell
} from '../utilities/tableRenderer'
import { TEMPLATE_JAPANESE } from '../utilities/templateConfig'
import renderTitleSection from '../utilities/titleSection'
import { TFunction } from 'i18next'

const renderContactDetails = ({
  section,
  resumeData,
  pdf,
  currentFontSize = FONT_SIZE_ENUMS.small,
  currentFont,
  startY,
  keys,
  watermark,
  customFieldViewData,
  dateFormatContent,
  extraData,
  t
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  pdf: jsPDF
  currentFontSize?: number
  currentFont: string
  startY: number
  keys: IKeyTemplate
  watermark: string
  dateFormatContent?: ISelectOption
  customFieldViewData?: CustomFieldViewType[]
  extraData?: {
    [type: string]: ISelectOption[]
  }
  t?: TFunction
}) => {
  let endYSection = 0

  const firstColumnWidth = TEMPLATE_JAPANESE.firstColumnWidth
  const columnWidth =
    PDF_VIEW_ENUMS.width -
    (PDF_VIEW_ENUMS.paddingLeft + PDF_VIEW_ENUMS.paddingRight) -
    firstColumnWidth

  const { yStart: yStartTitle, hTitle } = renderTitleSection({
    title: keys.contactDetailTitle,
    pdf,
    pdfPosition: startY + TEMPLATE_JAPANESE.sectionMargin,
    watermark,
    fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
    currentFont,
    hasBorderBottom: false
  })

  const yEndTitle = yStartTitle + hTitle
  let yAfterParagraph = 0

  const tables = mappingData({
    resumeData,
    section,
    customFieldViewData,
    keys,
    extraData,
    dateFormatContent,
    watermark,
    t
  })

  tables.forEach((table, index) => {
    let y =
      index === 0
        ? yEndTitle -
          TEMPLATE_JAPANESE.sectionMargin +
          TEMPLATE_JAPANESE.titleSectionMargin
        : yAfterParagraph
    endYSection = tableRenderer({
      pdf,
      startY: y,
      body: table,
      currentFontSize,
      currentFont,
      firstColumnWidth,
      columnWidth,
      onWillDrawCell: (data) => {
        yAfterParagraph = 0
        const raw1 = data.row.raw[1]
        if (
          typeof raw1 === 'object' &&
          raw1?.richContent &&
          data.column.index === 0
        ) {
          data.cell.styles.lineWidth = 0 // Remove border
        }
        if (
          typeof raw1 === 'object' &&
          raw1?.richContent &&
          data.column.index === 1
        ) {
          data.cell.styles.lineWidth = 0 // Remove border
          const height = estimateRichContentHeight(
            pdf,
            raw1?.richContent,
            data.cell.width,
            currentFontSize,
            currentFont
          )
          data.cell.height = height + 8 // add padding,
        }
      },
      onDidDrawCell: (data) => {
        const { row, column, cell } = data
        const raw1 = row.raw[1]
        if (
          typeof raw1 === 'object' &&
          raw1?.richContent &&
          column.index === 1
        ) {
          const x = cell.x
          // Remove border
          cell.styles.lineWidth = 0
          cell.styles.lineColor = [255, 255, 255]
          const pages = renderRichContentToPDF({
            pdf,
            blocks: raw1?.richContent,
            currentFont,
            startY: cell.y + cell.padding('top'),
            startX: x,
            currentFontSize,
            maxWidth: cell.width - cell.padding('left') - cell.padding('right')
          })
          yAfterParagraph = drawBorderCell({
            pdf,
            pages,
            currentFont,
            cellY: cell.y,
            currentFontSize
          })
        }
      }
    })
  })

  return yAfterParagraph
    ? yAfterParagraph
    : endYSection
    ? endYSection
    : yEndTitle
}

export default renderContactDetails
