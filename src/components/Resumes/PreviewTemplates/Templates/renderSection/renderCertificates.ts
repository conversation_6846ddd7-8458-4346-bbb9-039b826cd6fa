import { TFunction } from 'i18next'
import { jsPDF } from 'jspdf'
import { ISelectOption } from '~/core/ui/Select'
import { CertificatesType } from '~/lib/features/candidates/types'
import {
  checkCertificateFieldFormatDate,
  getCertificateFieldFormatDate
} from '~/lib/features/candidates/utilities/format'
import {
  IResume,
  ISectionCustomFieldParamType
} from '~/lib/features/settings/profiles/edit/types'
import {
  FONT_SIZE_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'
import { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import tableRenderer, {
  didDrawHeader,
  drawBorderCell,
  estimateRichContentHeight,
  renderRichContentToPDF,
  willDrawCell
} from '../utilities/tableRenderer'
import { TEMPLATE_JAPANESE } from '../utilities/templateConfig'
import renderTitleSection from '../utilities/titleSection'

const renderCertificates = ({
  section,
  resumeData,
  pdf,
  currentFontSize = FONT_SIZE_ENUMS.small,
  currentFont,
  startY,
  dateFormatContent,
  keys,
  locale,
  extraData,
  watermark,
  t
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  pdf: jsPDF
  currentFontSize?: number
  currentFont: string
  startY: number
  dateFormatContent?: ISelectOption
  keys: IKeyTemplate
  locale: string
  extraData?: {
    [key: string]: ISelectOption[]
  }
  watermark: string
  t?: TFunction
}) => {
  const firstColumnWidth = TEMPLATE_JAPANESE.firstColumnWidth
  const columnWidth =
    PDF_VIEW_ENUMS.width -
    (PDF_VIEW_ENUMS.paddingLeft + PDF_VIEW_ENUMS.paddingRight) -
    firstColumnWidth

  let body: any = []
  let tables = []
  const certificates = resumeData?.permittedFields?.certificates?.value

  const { yStart: yStartTitle, hTitle } = renderTitleSection({
    title: keys.certificatesTitle,
    pdf,
    pdfPosition: startY + TEMPLATE_JAPANESE.sectionMargin,
    watermark,
    fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
    currentFont,
    hasBorderBottom: false
  })

  let yEndTitle = yStartTitle + hTitle
  let endYHeader = yEndTitle
  let yAfterParagraph = yEndTitle
  let endYSection = yEndTitle

  if (!certificates?.length) return yStartTitle

  for (let i = 0; i < certificates.length; i++) {
    const certificate = certificates[i] as CertificatesType
    let date = ''
    let bodyContent = []
    const isShowDateTime = checkCertificateFieldFormatDate({
      attributes: certificate
    })
    const isShowContent = certificate.certificateName || certificate.institution

    let configShowDateTime: {
      fontSize: number
      content: string
      isChange: boolean
    } = {
      fontSize: currentFontSize,
      content: '',
      isChange: false
    }

    if (isShowDateTime) {
      date = getCertificateFieldFormatDate({
        attributes: certificate,
        isTemplate: true,
        dateFormatContent: dateFormatContent?.value
      })
    }

    if (isShowContent) {
      const description = `${certificate.certificateName || keys.undefined}${
        certificate.institution ? ` · ${certificate.institution}` : ''
      }`

      const descriptionHTML = {
        contentType: 'paragraph',
        content: [{ text: description, fontStyle: 'bold' }]
      }

      bodyContent.push(descriptionHTML)
    }

    body.push([
      date,
      {
        content: '',
        richContent: bodyContent
      }
    ])

    tables.push(body)
    body = []
  }

  if (body.length) tables.push(body)

  tableRenderer({
    pdf,
    startY:
      yEndTitle -
      TEMPLATE_JAPANESE.sectionMargin +
      TEMPLATE_JAPANESE.titleSectionMargin,
    body: [
      [
        {
          content: '',
          type: 'header',
          richContent: [
            {
              contentType: 'paragraph',
              content: [
                {
                  text: t ? t('candidates:headerTable:date') : 'Date',
                  fontStyle: 'bold',
                  fontSize: currentFontSize
                }
              ]
            }
          ]
        },
        {
          content: '',
          type: 'header',
          richContent: [
            {
              contentType: 'paragraph',
              content: [
                {
                  text: t
                    ? t('candidates:headerTable:certificate')
                    : 'Certificate - Institution',
                  fontStyle: 'bold',
                  fontSize: currentFontSize
                }
              ]
            }
          ]
        }
      ]
    ],
    currentFontSize,
    currentFont,
    firstColumnWidth,
    columnWidth,
    onWillDrawCell: (data) => {
      yAfterParagraph = 0
      willDrawCell({ data, pdf, currentFont, currentFontSize })
    },
    onDidDrawCell: (data) => {
      endYHeader = didDrawHeader({
        data,
        pdf,
        currentFont,
        currentFontSize
      })
    }
  })

  // Render body
  tables.forEach((table, index) => {
    const y = index === 0 ? endYHeader : yAfterParagraph
    endYSection = tableRenderer({
      pdf,
      startY: y,
      body: table,
      currentFontSize,
      currentFont,
      firstColumnWidth,
      columnWidth,
      isResetY: index !== 0,
      colStyles: {
        col1Style: {
          halign: 'center'
        }
      },
      onWillDrawCell: (data) => {
        yAfterParagraph = 0
        const raw1 = data.row.raw[1]
        if (
          typeof raw1 === 'object' &&
          raw1?.richContent &&
          data.column.index === 0
        ) {
          data.cell.styles.lineWidth = 0 // Remove border
        }
        if (
          typeof raw1 === 'object' &&
          raw1?.richContent &&
          data.column.index === 1
        ) {
          data.cell.styles.lineWidth = 0 // Remove border
          const height = estimateRichContentHeight(
            pdf,
            raw1?.richContent,
            data.cell.width,
            currentFontSize,
            currentFont
          )
          data.cell.height = height + 8 // add padding,
        }
      },
      onDidDrawCell: (data) => {
        const { row, column, cell } = data
        const raw1 = row.raw[1]
        if (
          typeof raw1 === 'object' &&
          raw1?.richContent &&
          column.index === 1
        ) {
          const x = cell.x
          // Remove border
          cell.styles.lineWidth = 0
          cell.styles.lineColor = [255, 255, 255]
          const pages = renderRichContentToPDF({
            pdf,
            blocks: raw1?.richContent,
            currentFont,
            startY: cell.y + cell.padding('top'),
            startX: x,
            currentFontSize,
            maxWidth: cell.width - cell.padding('left') - cell.padding('right')
          })
          yAfterParagraph = drawBorderCell({
            pdf,
            pages,
            currentFont,
            cellY: cell.y,
            currentFontSize
          })
        }
      }
    })
  })

  return yAfterParagraph ? yAfterParagraph : endYSection
}

export default renderCertificates
