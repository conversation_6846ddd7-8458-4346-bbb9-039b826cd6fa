import {
  FetchNextPageOptions,
  InfiniteData,
  InfiniteQueryObserverResult
} from '@tanstack/react-query'
import { FC, useContext, useMemo } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import configuration from '~/configuration'
import {
  AGENCY_TENANT,
  PROFILE,
  COMPANY,
  CONTACT,
  JOB,
  PLACEMENT
} from '~/core/constants/enum'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import IconWrapper from '~/core/ui/IconWrapper'
import { ISelectOption } from '~/core/ui/Select'
import { TableInfinity } from '~/core/ui/TableInfinity'
import { TypographyText } from '~/core/ui/Text'
import { Toggle } from '~/core/ui/Toggle'
import { Tooltip } from '~/core/ui/Tooltip'
import { CustomFieldsManagementPermissionContext } from '~/features/settings/custom-fields'
import useHiringPortalSetting from '~/lib/features/settings/company-settings/hooks/useHiringPortalSetting'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import { IProfileFields } from '~/lib/features/settings/profile-fields/types'
import { getFormatRoleIds } from '~/lib/features/settings/profile-fields/utilities/common'
import {
  ADMIN_RECRUITING_RECRUITER_VALUE,
  ADMIN_RECRUITING_VALUE,
  EVERYONE_VALUE
} from '~/lib/features/settings/profile-fields/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

const wTable = 840

const ProfileFieldsTable: FC<{
  fieldType: string
  isFetching: boolean
  isLoading: boolean
  isFetchedAfterMount: boolean
  fetchNextPage: (
    options?: FetchNextPageOptions | undefined
  ) => Promise<InfiniteQueryObserverResult<any, unknown>>
  data: InfiniteData<any> | undefined
  updateProfileFields: (data: IProfileFields) => Promise<void>
  showVisibleToColumn?: boolean
  tab: string
}> = ({
  fieldType,
  isFetching,
  isLoading,
  isFetchedAfterMount,
  fetchNextPage,
  data,
  updateProfileFields,
  showVisibleToColumn = false,
  tab
}) => {
  const { t } = useTranslation()
  const customFieldsManagementPermissions = useContext(
    CustomFieldsManagementPermissionContext
  )
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const { enableHiringPortal } = useHiringPortalSetting()

  const onShowTooltipField = (objectKind: string) => {
    switch (objectKind) {
      case PROFILE:
        return t('tooltip:visibilityApplicant')

      case JOB:
        return t('tooltip:visibilityJobs')

      case COMPANY:
        return t('tooltip:visibilityCompanies')

      case CONTACT:
        return t('tooltip:visibilityContact')
      case PLACEMENT:
        return t('tooltip:visibilityPlacement')
      default:
        return ''
    }
  }
  const defaultColumns = useMemo(() => {
    return [
      {
        accessorKey: 'icon',
        header: () => <div />,
        footer: (props: { column: { id: string } }) => props.column.id,
        size: 16,
        enableResizing: false
      },
      {
        accessorKey: 'name',
        header: () => (
          <TypographyText className="text-left text-xs font-normal text-gray-600 dark:text-gray-300">
            <Trans
              i18nKey={'settings:custom_fields:tab:system_fields_table:name'}
            />
          </TypographyText>
        ),
        cell: (info: {
          row: { original: IProfileFields }
          getValue: Function
        }) => (
          <div className="flex items-center">
            <TypographyText className="mr-1.5 text-sm font-medium text-gray-900 dark:text-gray-300">
              {info.getValue()}
            </TypographyText>
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: 318,
        enableResizing: false
      },
      {
        accessorKey: 'visibility',
        header: () => (
          <div className="flex items-center space-x-2">
            <TypographyText className="text-left text-xs font-normal text-gray-600 dark:text-gray-300">
              <Trans i18nKey="settings:custom_fields:tab:system_fields_table:visibility" />
            </TypographyText>
            <Tooltip
              mode="icon"
              classNameConfig={{
                content: 'max-w-[380px]'
              }}
              content={onShowTooltipField(tab)}>
              <IconWrapper
                name="HelpCircle"
                size={14}
                className="cursor-pointer text-gray-400"
              />
            </Tooltip>
          </div>
        ),
        cell: (info: {
          row: { original: IProfileFields }
          getValue: Function
        }) => (
          <div>
            <Toggle
              isDisabled={
                !customFieldsManagementPermissions.update ||
                !info.row.original.visibilityChangeable
              }
              isChecked={info.row.original.visibility}
              name="visibility"
              onCheckedChange={(checked) => {
                updateProfileFields({
                  id: info.row.original.id,
                  visibility: checked
                })
              }}
              size="sm"
              toggle="trailing"
            />
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: 94,
        enableResizing: false
      }
    ]
  }, [data, updateProfileFields])

  const optionalColumns = useMemo(() => {
    return [
      {
        accessorKey: 'roleIds',
        header: () => (
          <TypographyText className="text-left text-xs font-normal text-gray-600 dark:text-gray-300">
            <Trans
              i18nKey={
                'settings:custom_fields:tab:system_fields_table:visible_to'
              }
            />
          </TypographyText>
        ),
        cell: (info: {
          row: { original: IProfileFields }
          getValue: Function
        }) => {
          const ROLE_PROFILE_FIELDS = [
            {
              value: '1',
              supportingObj: {
                name: `${t('settings:custom_fields:permissions:everyoneName')}`,
                description: `${t(
                  'settings:custom_fields:permissions:everyoneDescription'
                )}`
              }
            },
            ...(info.row.original.roleChangeable
              ? [
                  {
                    value: '4',
                    supportingObj: {
                      name: `${t(
                        'settings:custom_fields:permissions:adminMemberName'
                      )}`
                    }
                  }
                ]
              : [])
            // {
            //   value: '3',
            //   supportingObj: {
            //     name: `${t('settings:custom_fields:permissions:adminName')}`
            //   }
            // }
          ]
          const newRoleIds = getFormatRoleIds(
            (info.row.original?.roleIds || '').toString()
          )
          const filterRoles = ROLE_PROFILE_FIELDS.filter(
            (item) => item.value == newRoleIds
          )?.[0]

          return (
            <>
              {info.row.original.visibility ? (
                <ComboboxSelect
                  isSearchable={false}
                  options={ROLE_PROFILE_FIELDS}
                  type="unstyled"
                  dropdownMenuClassName="min-w-[200px]"
                  isDisabled={
                    customFieldsManagementPermissions.update
                      ? !info.row.original.roleChangeable
                      : true
                  }
                  isClearable={false}
                  value={filterRoles}
                  onChange={(option, actionMeta) => {
                    const singleValue = option as ISelectOption
                    const onChangeRoles = (param: string) => {
                      switch (param) {
                        case '1':
                          return EVERYONE_VALUE
                        case '2':
                          return ADMIN_RECRUITING_VALUE
                        // case '3':
                        //   return ADMIN_VALUE
                        case '4':
                          return ADMIN_RECRUITING_RECRUITER_VALUE
                        default:
                          return []
                      }
                    }
                    updateProfileFields({
                      id: info.row.original.id,
                      roleIds: onChangeRoles(
                        actionMeta?.action === 'select-option'
                          ? singleValue.value
                          : ''
                      )
                    })
                  }}
                />
              ) : (
                ''
              )}
            </>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: 143,
        enableResizing: false
      },
      {
        accessorKey: 'profile',
        header: () => (
          <div className="flex items-center space-x-2">
            <Tooltip
              content={t(
                'settings:custom_fields:tab:system_fields_table:profile'
              )}>
              <TypographyText className="line-clamp-1 text-left text-xs font-normal text-gray-600 dark:text-gray-300">
                <Trans i18nKey="settings:custom_fields:tab:system_fields_table:profile" />
              </TypographyText>
            </Tooltip>
            <Tooltip
              mode="icon"
              classNameConfig={{
                content: 'max-w-[380px]'
              }}
              content={t('tooltip:visibilityProfile')}>
              <IconWrapper
                name="HelpCircle"
                size={14}
                className="cursor-pointer text-gray-400"
              />
            </Tooltip>
          </div>
        ),
        cell: (info: {
          row: { original: IProfileFields }
          getValue: Function
        }) => (
          <>
            {info.row.original.visibility ? (
              <Toggle
                isDisabled={
                  !customFieldsManagementPermissions.update ||
                  !info.row.original.employeeProfileChangeable
                }
                isChecked={info.row.original.visibleToEmployeeProfile}
                name="visibleToEmployeeProfile"
                onCheckedChange={(checked) => {
                  updateProfileFields({
                    id: info.row.original.id,
                    visibleToEmployeeProfile: checked
                  })
                }}
                size="sm"
                toggle="trailing"
              />
            ) : null}
          </>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: !isCompanyKind && showVisibleToColumn ? 94 : 418
      }
    ]
  }, [data, updateProfileFields])

  const clientColumn = useMemo(() => {
    return [
      {
        accessorKey: 'clientUserVisibility',
        header: () => (
          <div className="flex items-center space-x-2">
            <Tooltip
              content={t(
                'settings:custom_fields:tab:system_fields_table:client'
              )}>
              <TypographyText className="line-clamp-1 text-left text-xs font-normal text-gray-600 dark:text-gray-300">
                <Trans i18nKey="settings:custom_fields:tab:system_fields_table:client" />
              </TypographyText>
            </Tooltip>
            <Tooltip
              mode="icon"
              classNameConfig={{
                content: 'max-w-[380px]'
              }}
              content={t('tooltip:clientVisibilityApplicant')}>
              <IconWrapper
                name="HelpCircle"
                size={14}
                className="cursor-pointer text-gray-400"
              />
            </Tooltip>
          </div>
        ),
        cell: (info: {
          row: { original: IProfileFields }
          getValue: Function
        }) => (
          <div
            className={`${!info?.row?.original?.visibility ? 'hidden' : ''}`}>
            <Toggle
              isDisabled={
                !customFieldsManagementPermissions.update ||
                !info.row.original.clientVisibilityChangeable
              }
              isChecked={info.row.original.clientUserVisibility}
              name="clientUserVisibility"
              onCheckedChange={(checked) => {
                updateProfileFields({
                  id: info.row.original.id,
                  clientUserVisibility: checked
                })
              }}
              size="sm"
              toggle="trailing"
            />
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: !isCompanyKind && showVisibleToColumn ? 94 : 321 // 412 = name + visibility
      }
    ]
  }, [data, updateProfileFields])

  const jobSystemFieldsColumns = useMemo(() => {
    return [
      {
        accessorKey: 'careerSite',
        header: () => (
          <div className="flex items-center space-x-2">
            <TypographyText className="text-left text-xs font-normal text-gray-600 dark:text-gray-300">
              <Trans i18nKey="settings:custom_fields:tab:system_fields_table:careerSite" />
            </TypographyText>
            <Tooltip
              mode="icon"
              classNameConfig={{
                content: 'max-w-[380px]'
              }}
              content={t(
                'settings:custom_fields:tab:system_fields_table:careerSiteHelpText'
              )}>
              <IconWrapper
                name="HelpCircle"
                size={14}
                className="cursor-pointer text-gray-400"
              />
            </Tooltip>
          </div>
        ),
        cell: (info: {
          row: { original: IProfileFields }
          getValue: Function
        }) => (
          <>
            {info.row.original.visibility && (
              <Toggle
                isDisabled={!customFieldsManagementPermissions.update}
                isChecked={info.row.original.careerSiteVisibility}
                name="careerSiteVisibility"
                onCheckedChange={(checked) => {
                  updateProfileFields({
                    id: info.row.original.id,
                    careerSiteVisibility: checked
                  })
                }}
                size="sm"
                toggle="trailing"
              />
            )}
          </>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: 136,
        enableResizing: false
      },
      {
        accessorKey: 'filter',
        header: () => (
          <div className="flex items-center space-x-2">
            <TypographyText className="text-left text-xs font-normal text-gray-600 dark:text-gray-300">
              <Trans i18nKey="settings:custom_fields:tab:system_fields_table:filter" />
            </TypographyText>
            <Tooltip
              mode="icon"
              classNameConfig={{
                content: 'max-w-[380px]'
              }}
              content={t(
                'settings:custom_fields:tab:system_fields_table:filterHelpText'
              )}>
              <IconWrapper
                name="HelpCircle"
                size={14}
                className="cursor-pointer text-gray-400"
              />
            </Tooltip>
          </div>
        ),
        cell: (info: {
          row: { original: IProfileFields }
          getValue: Function
        }) => (
          <>
            {info?.row?.original?.careerSiteVisibility &&
              !info?.row?.original.filterVisibilityHidden && (
                <Toggle
                  isDisabled={!customFieldsManagementPermissions.update}
                  isChecked={info.row.original.filterVisibility}
                  name="filterVisibility"
                  onCheckedChange={(checked) => {
                    updateProfileFields({
                      id: info.row.original.id,
                      filterVisibility: checked
                    })
                  }}
                  size="sm"
                  toggle="trailing"
                />
              )}
          </>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: 188
      }
    ]
  }, [data, updateProfileFields])

  return (
    <TableInfinity
      tableConfig={{
        defaultFirstLoadingSize: configuration.defaultTableMountPageSize,
        pageSize:
          fieldType === 'profiles'
            ? configuration.defaultPageSize * 2
            : configuration.defaultPageSize,
        useInfinity: false
      }}
      dataQuery={{
        isFetching,
        isLoading,
        isFetchedAfterMount,
        fetcher: {
          fetchNextPage
        },
        data
      }}
      columns={
        // isCompanyKind && tab === PROFILE
        //   ? [
        //       ...defaultColumns,
        //       ...jobSystemFieldsColumns,
        //       ...clientColumn,
        //       ...optionalColumns
        //     ]
        //   : [...defaultColumns, ...jobSystemFieldsColumns, ...optionalColumns]
        [
          ...defaultColumns,
          ...jobSystemFieldsColumns,
          ...optionalColumns,
          ...clientColumn
        ]
      }
      classNameTable="max-w-full bg-white"
      columnVisibility={{
        roleIds: !isCompanyKind && showVisibleToColumn,
        // check permission of tenantPlanShow
        profile:
          !isCompanyKind &&
          showVisibleToColumn &&
          !!isFeatureEnabled(PLAN_FEATURE_KEYS.employee_profile) &&
          !!isUnLockFeature(PLAN_FEATURE_KEYS.employee_profile),
        careerSite: tab === JOB,
        clientUserVisibility:
          tab === PROFILE && (isCompanyKind || enableHiringPortal),
        filter: tab === JOB
      }}
    />
  )
}

export default ProfileFieldsTable
